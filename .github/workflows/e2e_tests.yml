name: e2e tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
  pull_request:

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: e2e tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.4.5"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up `.env` file
        run: |
          cd .devcontainer
          cp .env.example .env

      - name: Start mini rails app
        run: |
          cd .devcontainer
          source .env
          echo "=== Environment variables ==="
          echo "SENTRY_E2E_RAILS_APP_PORT: ${SENTRY_E2E_RAILS_APP_PORT}"
          echo "SENTRY_DSN: ${SENTRY_DSN}"
          echo "=== Starting Rails mini app ==="
          docker compose --profile e2e up -d sentry-rails-mini
          echo "=== Checking container status ==="
          docker compose --profile e2e ps
          echo "=== Rails mini app logs ==="
          docker compose --profile e2e logs sentry-rails-mini

      - name: Start mini svelte app
        run: |
          cd .devcontainer
          source .env
          echo "=== Starting Svelte mini app ==="
          docker compose --profile e2e up -d sentry-svelte-mini
          echo "=== Checking container status ==="
          docker compose --profile e2e ps
          echo "=== Svelte mini app logs ==="
          docker compose --profile e2e logs sentry-svelte-mini

      - name: Wait for services to be healthy
        run: |
          cd .devcontainer
          source .env

          echo "=== Debugging container status ==="
          docker compose --profile e2e ps
          echo "=== Checking port bindings ==="
          docker compose --profile e2e port sentry-rails-mini 4000 || echo "Rails port binding failed"
          docker compose --profile e2e port sentry-svelte-mini 4001 || echo "Svelte port binding failed"

          echo "=== Latest container logs ==="
          echo "Rails mini logs:"
          docker compose --profile e2e logs --tail=50 sentry-rails-mini
          echo "Svelte mini logs:"
          docker compose --profile e2e logs --tail=50 sentry-svelte-mini

          echo "=== Waiting for Docker health checks ==="
          timeout 120 bash -c "
            while true; do
              rails_health=\$(docker compose --profile e2e ps --format json sentry-rails-mini | jq -r '.[0].Health // \"none\"')
              svelte_health=\$(docker compose --profile e2e ps --format json sentry-svelte-mini | jq -r '.[0].Health // \"none\"')
              echo \"Rails health: \$rails_health, Svelte health: \$svelte_health\"

              if [[ \"\$rails_health\" == \"healthy\" && \"\$svelte_health\" == \"healthy\" ]]; then
                echo \"Both containers are healthy!\"
                break
              fi

              if [[ \"\$rails_health\" == \"unhealthy\" || \"\$svelte_health\" == \"unhealthy\" ]]; then
                echo \"One or more containers are unhealthy\"
                docker compose --profile e2e ps
                docker compose --profile e2e logs sentry-rails-mini
                docker compose --profile e2e logs sentry-svelte-mini
                exit 1
              fi

              sleep 5
            done
          "

          echo "Waiting for Rails mini app to be ready on port ${SENTRY_E2E_RAILS_APP_PORT}..."
          timeout 60 bash -c "
            while true; do
              echo 'Attempting to connect to Rails app...'
              response=\$(curl -s http://localhost:${SENTRY_E2E_RAILS_APP_PORT}/health || echo 'connection_failed')
              echo \"Response: \$response\"
              if echo \"\$response\" | grep -q 'ok'; then
                echo 'Rails app responded with ok'
                break
              fi
              echo 'Waiting 2 seconds...'
              sleep 2
            done
          "
          echo "✅ Rails mini app is ready"

          echo "Waiting for Svelte mini app to be ready on port ${SENTRY_E2E_SVELTE_APP_PORT}..."
          timeout 60 bash -c "
            while true; do
              echo 'Attempting to connect to Svelte app...'
              response=\$(curl -s http://localhost:${SENTRY_E2E_SVELTE_APP_PORT}/health || echo 'connection_failed')
              echo \"Response: \$response\"
              if echo \"\$response\" | grep -q 'ok'; then
                echo 'Svelte app responded with ok'
                break
              fi
              echo 'Waiting 2 seconds...'
              sleep 2
            done
          "
          echo "✅ Svelte mini app is ready"

          echo "All services are healthy!"

      - name: Final status check before tests
        run: |
          cd .devcontainer
          source .env
          echo "=== Final container status ==="
          docker compose --profile e2e ps
          echo "=== Final logs check ==="
          echo "Rails mini logs:"
          docker compose --profile e2e logs --tail=20 sentry-rails-mini
          echo "Svelte mini logs:"
          docker compose --profile e2e logs --tail=20 sentry-svelte-mini

      - name: Run e2e tests
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e run --rm sentry-test bundle exec rake
        env:
          SENTRY_E2E_RAILS_APP_URL: http://sentry-rails-mini:4000
          SENTRY_E2E_SVELTE_APP_URL: http://sentry-svelte-mini:4001

      - name: Stop e2e services
        if: always()
        run: |
          cd .devcontainer
          source .env
          docker compose --profile e2e down

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            spec/apps/rails-mini/log/sentry_debug_events.log
          retention-days: 7
