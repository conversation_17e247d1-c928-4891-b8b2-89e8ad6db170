#!/bin/bash

set -e

echo "=== Rails Mini Container Starting ==="
echo "Timestamp: $(date)"
echo "Working directory: $(pwd)"
echo "User: $(whoami)"
echo "Ruby version: $(ruby --version)"

echo "=== Environment Variables ==="
echo "PORT: ${PORT:-not set}"
echo "SENTRY_DSN: ${SENTRY_DSN:-not set}"
echo "SENTRY_E2E_RAILS_APP_PORT: ${SENTRY_E2E_RAILS_APP_PORT:-not set}"
echo "RAILS_ENV: ${RAILS_ENV:-not set}"

echo "=== Setting up log directory permissions ==="
sudo chown -R sentry:sentry /workspace/sentry/log
ls -la /workspace/sentry/log/

echo "=== Changing to Rails mini directory ==="
cd /workspace/sentry/spec/apps/rails-mini
echo "Current directory: $(pwd)"
ls -la

echo "=== Installing Ruby dependencies for Rails mini ==="
bundle install --verbose

echo "=== Checking if Gemfile.lock exists ==="
if [ -f "Gemfile.lock" ]; then
    echo "✅ Gemfile.lock found"
    echo "Bundled gems:"
    bundle list
else
    echo "❌ Gemfile.lock not found"
fi

echo "=== About to execute command: $@ ==="
echo "=== Rails Mini Container Setup Complete ==="

exec "$@"
