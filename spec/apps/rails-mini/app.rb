# frozen_string_literal: true

require "bundler/setup"

Bundler.require

ENV["RAILS_ENV"] = "development"

require "action_controller"

class RailsMiniApp < Rails::Application
  config.hosts = nil
  config.secret_key_base = "test_secret_key_base_for_rails_mini_app"
  config.eager_load = false
  config.logger = Logger.new($stdout)
  config.log_level = :debug

  # Disable some Rails features we don't need
  config.api_only = true
  config.force_ssl = false

  initializer :validate_dsn, before: :configure_sentry do
    dsn_string = ENV["SENTRY_DSN"]

    if dsn_string.nil? || dsn_string.empty?
      puts "ERROR: SENTRY_DSN environment variable is required but not set"
      exit(1)
    end

    begin
      dsn = Sentry::DSN.new(dsn_string)
      unless dsn.valid?
        puts "ERROR: Invalid SENTRY_DSN: #{dsn_string}"
        puts "DSN must include host, path, public_key, and project_id"
        exit(1)
      end
    rescue URI::InvalidURIError => e
      puts "ERROR: Invalid SENTRY_DSN format: #{e.message}"
      exit(1)
    rescue => e
      puts "ERROR: Failed to parse SENTRY_DSN: #{e.message}"
      exit(1)
    end
  end

  # Configure Sentry
  initializer :configure_sentry do
    Sentry.init do |config|
      config.dsn = ENV["SENTRY_DSN"]
      config.breadcrumbs_logger = [:active_support_logger, :http_logger, :redis_logger]
      config.traces_sample_rate = 1.0
      config.send_default_pii = true
      config.sdk_logger.level = ::Logger::DEBUG
      config.sdk_logger = Sentry::Logger.new($stdout)
      config.debug = true
      config.include_local_variables = true
      config.release = "sentry-ruby-rails-mini-#{Time.now.utc}"

      config.transport.transport_class = Sentry::DebugTransport
      config.sdk_debug_transport_log_file = "/workspace/sentry/log/sentry_debug_events.log"
      config.background_worker_threads = 0
    end
  end
end

class ErrorController < ActionController::Base
  before_action :set_cors_headers

  def error
    result = 1 / 0
    render json: { result: result }
  end

  private

  def set_cors_headers
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, sentry-trace, baggage'
  end
end

class EventsController < ActionController::Base
  before_action :set_cors_headers

  def health
    puts "=== Health check requested at #{Time.now.utc.iso8601} ==="

    health_data = {
      status: "ok",
      timestamp: Time.now.utc.iso8601,
      sentry_initialized: Sentry.initialized?,
      log_file_writable: check_log_file_writable,
      environment: {
        rails_env: ENV['RAILS_ENV'],
        port: ENV['PORT'],
        sentry_dsn_set: !ENV['SENTRY_DSN'].nil? && !ENV['SENTRY_DSN'].empty?
      },
      process: {
        pid: Process.pid,
        working_directory: Dir.pwd
      }
    }

    puts "Health data: #{health_data.to_json}"
    render json: health_data
  end

  def trace_headers
    headers = Sentry.get_trace_propagation_headers || {}
    render json: { headers: headers }
  end

  private

  def check_log_file_writable
    log_file_path = "/workspace/sentry/log/sentry_debug_events.log"
    File.writable?(File.dirname(log_file_path)) &&
      (!File.exist?(log_file_path) || File.writable?(log_file_path))
  rescue
    false
  end

  def set_cors_headers
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, sentry-trace, baggage'
  end
end

RailsMiniApp.initialize!

RailsMiniApp.routes.draw do
  get '/health', to: 'events#health'
  get '/error', to: 'error#error'
  get '/trace_headers', to: 'events#trace_headers'

  # Add CORS headers for cross-origin requests from JS app
  match '*path', to: proc { |env|
    [200, {
      'Access-Control-Allow-Origin' => '*',
      'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers' => 'Content-Type, Authorization, sentry-trace, baggage',
      'Content-Type' => 'application/json'
    }, ['{"status": "ok"}']]
  }, via: :options
end

if __FILE__ == $0
  require "rack"
  require "rack/handler/puma"

  puts "=== Rails Mini App Starting ==="
  puts "Timestamp: #{Time.now.utc.iso8601}"
  puts "Ruby version: #{RUBY_VERSION}"
  puts "Rails version: #{Rails::VERSION::STRING}"
  puts "Working directory: #{Dir.pwd}"

  puts "=== Environment Check ==="
  puts "RAILS_ENV: #{ENV['RAILS_ENV']}"
  puts "PORT: #{ENV['PORT'] || 'not set (will default to 4000)'}"
  puts "SENTRY_DSN: #{ENV['SENTRY_DSN'] ? 'set' : 'NOT SET'}"
  puts "SENTRY_E2E_RAILS_APP_PORT: #{ENV['SENTRY_E2E_RAILS_APP_PORT'] || 'not set'}"

  puts "=== Sentry Configuration Check ==="
  puts "Sentry initialized: #{Sentry.initialized?}"
  if Sentry.initialized?
    puts "Sentry DSN configured: #{Sentry.configuration.dsn ? 'yes' : 'no'}"
    puts "Sentry debug mode: #{Sentry.configuration.debug}"
  end

  puts "=== Log File Check ==="
  log_file_path = "/workspace/sentry/log/sentry_debug_events.log"
  puts "Log file path: #{log_file_path}"
  puts "Log file exists: #{File.exist?(log_file_path)}"
  puts "Log file writable: #{File.writable?(File.dirname(log_file_path))}"

  port = ENV.fetch("PORT", "4000").to_i
  puts "=== Starting Puma Server ==="
  puts "Host: 0.0.0.0"
  puts "Port: #{port}"
  puts "=== Server starting... ==="

  begin
    Rack::Handler::Puma.run(RailsMiniApp, Host: "0.0.0.0", Port: port, Verbose: true)
  rescue => e
    puts "=== ERROR: Failed to start server ==="
    puts "Error: #{e.class}: #{e.message}"
    puts "Backtrace:"
    puts e.backtrace.join("\n")
    exit(1)
  end
end
